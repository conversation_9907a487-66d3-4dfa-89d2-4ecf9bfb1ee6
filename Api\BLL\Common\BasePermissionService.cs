using Common.Exceptions;
using Common.JWT;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace BLL.Common
{
    /// <summary>
    /// 权限服务基类
    /// 提供统一的权限控制和数据过滤功能
    /// </summary>
    public abstract class BasePermissionService(UserDAL userDAL, SysUserDAL sysUserDAL)
    {
        protected readonly UserDAL _userDAL = userDAL;
        protected readonly SysUserDAL _sysUserDAL = sysUserDAL;

        #region 权限检查方法


        /// <summary>
        /// 检查当前用户是否可以访问所有数据
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否可以访问所有数据</returns>
        protected bool CanAccessAllData(UserInfo currentUserInfo)
        {
            return currentUserInfo.UserType == 1; // 超级管理员
        }

        /// <summary>
        /// 检查当前用户是否可以访问员工数据
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否可以访问员工数据</returns>
        protected bool CanAccessEmployeeData(UserInfo currentUserInfo)
        {
            return currentUserInfo.UserType == 1 || currentUserInfo.UserType == 2; // 超管或管理员
        }

        /// <summary>
        /// 检查当前用户是否只能访问自己的数据
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否只能访问自己的数据</returns>
        protected bool CanAccessOwnDataOnly(UserInfo currentUserInfo)
        {
            return currentUserInfo.UserType == 3; // 员工
        }

        #endregion

        #region 数据权限过滤方法

        /// <summary>
        /// 根据用户权限获取可访问的用户ID列表
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>可访问的用户ID列表，null表示不限制</returns>
        protected async Task<List<string>?> GetAccessibleUserIdsAsync(UserInfo currentUserInfo)
        {
            switch (currentUserInfo.UserType)
            {
                case 1: // 超级管理员 - 可以查看所有用户
                    return null; // 返回null表示不限制用户ID

                case 2: // 管理员 - 可以查看所有员工及其用户
                    var allEmployeeUsers = await GetAllEmployeeUsersAsync();
                    return [.. allEmployeeUsers.Select(u => u.Id)];

                case 3: // 员工 - 只能查看自己绑定用户
                    var myUsers = await _userDAL.GetByEmployeeIdAsync(currentUserInfo.UserId);
                    return [.. myUsers.Select(u => u.Id)];

                default:
                    throw new BusinessException("无效的用户类型");
            }
        }

        /// <summary>
        /// 根据用户权限获取可访问的员工ID列表
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>可访问的员工ID列表，null表示不限制</returns>
        protected async Task<List<string>?> GetAccessibleEmployeeIdsAsync(UserInfo currentUserInfo)
        {
            switch (currentUserInfo.UserType)
            {
                case 1: // 超级管理员 - 可以查看所有员工
                    return null; // 返回null表示不限制

                case 2: // 管理员 - 可以查看自己的下级员工
                    var subordinateEmployees = await _sysUserDAL.GetByParentUserIdAsync(currentUserInfo.UserId);
                    return [.. subordinateEmployees.Select(e => e.UserId)];

                case 3: // 员工 - 只能查看自己
                    return [currentUserInfo.UserId];

                default:
                    throw new BusinessException("无效的用户类型");
            }
        }

        /// <summary>
        /// 获取用户访问过滤表达式
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>用户过滤表达式</returns>
        protected async Task<Expression<Func<User, bool>>> GetUserAccessFilterAsync(UserInfo currentUserInfo)
        {
            switch (currentUserInfo.UserType)
            {
                case 1: // 超级管理员：所有用户
                    return u => true;

                case 2: // 管理员：所有员工的用户
                    return u => !string.IsNullOrEmpty(u.EmployeeId);

                case 3: // 员工：自己的用户
                    return u => u.EmployeeId == currentUserInfo.UserId;

                default:
                    return u => false; // 默认拒绝
            }
        }

        /// <summary>
        /// 获取通用实体访问过滤表达式
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="userIdProperty">用户ID属性名</param>
        /// <returns>实体过滤表达式</returns>
        protected async Task<Expression<Func<T, bool>>> GetEntityAccessFilterAsync<T>(
            UserInfo currentUserInfo,
            string userIdProperty = "UserId")
        {
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);

            if (accessibleUserIds == null)
            {
                // 超级管理员，不限制
                return entity => true;
            }

            // 构建表达式：entity => accessibleUserIds.Contains(entity.UserId)
            var parameter = Expression.Parameter(typeof(T), "entity");
            var property = Expression.Property(parameter, userIdProperty);
            var containsMethod = typeof(List<string>).GetMethod("Contains", new[] { typeof(string) });
            var containsCall = Expression.Call(
                Expression.Constant(accessibleUserIds),
                containsMethod!,
                property);

            return Expression.Lambda<Func<T, bool>>(containsCall, parameter);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取所有员工的用户
        /// </summary>
        /// <returns>所有员工的用户列表</returns>
        protected async Task<List<User>> GetAllEmployeeUsersAsync()
        {
            return await _userDAL.GetQueryable()
                .Where(u => !string.IsNullOrEmpty(u.EmployeeId))
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有员工
        /// </summary>
        /// <returns>所有员工列表</returns>
        protected async Task<List<Entity.Entitys.SysEntity.SysUser>> GetAllEmployeesAsync()
        {
            return await _sysUserDAL.GetQueryable()
                .Where(u => u.UserType == 3)
                .ToListAsync();
        }

        /// <summary>
        /// 验证用户是否有权限访问指定数据
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>是否有权限</returns>
        protected async Task<bool> HasPermissionToAccessUserAsync(UserInfo currentUserInfo, string targetUserId)
        {
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);

            // 超级管理员可以访问所有数据
            if (accessibleUserIds == null)
                return true;

            // 检查目标用户是否在可访问范围内
            return accessibleUserIds.Contains(targetUserId);
        }

        /// <summary>
        /// 应用用户权限过滤到查询
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="query">原始查询</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="userIdProperty">用户ID属性名</param>
        /// <returns>应用权限过滤后的查询</returns>
        protected async Task<IQueryable<T>> ApplyUserPermissionFilterAsync<T>(
            IQueryable<T> query,
            UserInfo currentUserInfo,
            string userIdProperty = "UserId")
        {
            var filter = await GetEntityAccessFilterAsync<T>(currentUserInfo, userIdProperty);
            return query.Where(filter);
        }

        #endregion

        #region 权限验证方法

        /// <summary>
        /// 验证并抛出权限异常
        /// </summary>
        /// <param name="hasPermission">是否有权限</param>
        /// <param name="errorMessage">错误消息</param>
        protected void ValidatePermission(bool hasPermission, string errorMessage = "权限不足")
        {
            if (!hasPermission)
                throw new BusinessException(errorMessage);
        }

        /// <summary>
        /// 验证用户类型权限
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="requiredUserTypes">需要的用户类型</param>
        /// <param name="errorMessage">错误消息</param>
        protected void ValidateUserTypePermission(
            UserInfo currentUserInfo,
            byte[] requiredUserTypes,
            string errorMessage = "权限不足")
        {
            ValidatePermission(
                requiredUserTypes.Contains(currentUserInfo.UserType),
                errorMessage);
        }

        #endregion

        #region 数据过滤扩展方法

        /// <summary>
        /// 为查询应用用户ID列表过滤
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="query">原始查询</param>
        /// <param name="accessibleUserIds">可访问的用户ID列表，null表示不限制</param>
        /// <param name="userIdProperty">用户ID属性名</param>
        /// <returns>过滤后的查询</returns>
        protected IQueryable<T> ApplyUserIdFilter<T>(
            IQueryable<T> query,
            List<string>? accessibleUserIds,
            string userIdProperty = "UserId")
        {
            if (accessibleUserIds == null)
                return query; // 不限制

            // 使用反射获取属性
            var parameter = Expression.Parameter(typeof(T), "entity");
            var property = Expression.Property(parameter, userIdProperty);
            var containsMethod = typeof(List<string>).GetMethod("Contains", new[] { typeof(string) });
            var containsCall = Expression.Call(
                Expression.Constant(accessibleUserIds),
                containsMethod!,
                property);
            var lambda = Expression.Lambda<Func<T, bool>>(containsCall, parameter);

            return query.Where(lambda);
        }

        /// <summary>
        /// 检查数据是否在用户权限范围内
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="dataUserId">数据关联的用户ID</param>
        /// <returns>是否在权限范围内</returns>
        protected async Task<bool> IsDataInUserScopeAsync(UserInfo currentUserInfo, string dataUserId)
        {
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);

            // 超级管理员可以访问所有数据
            if (accessibleUserIds == null)
                return true;

            return accessibleUserIds.Contains(dataUserId);
        }

        /// <summary>
        /// 批量检查数据是否在用户权限范围内
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="dataUserIds">数据关联的用户ID列表</param>
        /// <returns>在权限范围内的用户ID列表</returns>
        protected async Task<List<string>> FilterDataInUserScopeAsync(UserInfo currentUserInfo, List<string> dataUserIds)
        {
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);

            // 超级管理员可以访问所有数据
            if (accessibleUserIds == null)
                return dataUserIds;

            return [.. dataUserIds.Where(id => accessibleUserIds.Contains(id))];
        }

        #endregion

        #region 统计数据权限过滤

        /// <summary>
        /// 获取统计数据的用户范围过滤条件
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>用户范围过滤条件</returns>
        protected async Task<Expression<Func<User, bool>>> GetStatisticsUserFilterAsync(UserInfo currentUserInfo)
        {
            return await GetUserAccessFilterAsync(currentUserInfo);
        }

        /// <summary>
        /// 为统计查询应用权限过滤
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="query">统计查询</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="userIdProperty">用户ID属性名</param>
        /// <returns>应用权限过滤后的查询</returns>
        protected async Task<IQueryable<T>> ApplyStatisticsPermissionFilterAsync<T>(
            IQueryable<T> query,
            UserInfo currentUserInfo,
            string userIdProperty = "UserId")
        {
            return await ApplyUserPermissionFilterAsync(query, currentUserInfo, userIdProperty);
        }

        #endregion
    }
}
