using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;

namespace BLL.VideoService
{
    /// <summary>
    /// 微信访问令牌业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class WechatAccessTokenService(WechatAccessTokenDAL wechatAccessTokenDAL, SysLogService logService)
    {
        private readonly WechatAccessTokenDAL _wechatAccessTokenDAL = wechatAccessTokenDAL;
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 创建访问令牌
        /// </summary>
        /// <param name="createDto">创建访问令牌DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>令牌ID</returns>
        public async Task<int> CreateAccessTokenAsync(WechatAccessTokenCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证令牌类型
            if (string.IsNullOrEmpty(createDto.TokenType))
                throw new BusinessException("令牌类型不能为空");

            // 验证访问令牌
            if (string.IsNullOrEmpty(createDto.AccessToken))
                throw new BusinessException("访问令牌不能为空");

            // 验证过期时间
            if (createDto.ExpiresIn <= 0)
                throw new BusinessException("过期时间必须大于0");

            // 创建微信访问令牌实体
            var wechatAccessToken = new WechatAccessToken
            {
                AppId = createDto.AppId,
                TokenType = createDto.TokenType,
                AccessToken = createDto.AccessToken,
                ExpiresAt = createDto.ExpiresAt,
                ExpiresIn = createDto.ExpiresIn,
                ExpiresTime = createDto.ExpiresIn.HasValue ? DateTime.Now.AddSeconds((double)createDto.ExpiresIn.Value) : createDto.ExpiresAt,
                RefreshToken = createDto.RefreshToken,
                Scope = createDto.Scope,
                IsValid = createDto.IsValid,
                IsActive = 1, // 默认激活
                IsExpired = 0, // 默认未过期
                ObtainTime = DateTime.Now,
                CreateTime = DateTime.Now
            };

            // 添加访问令牌
            await _wechatAccessTokenDAL.AddAsync(wechatAccessToken);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信令牌",
                Operation = "创建访问令牌",
                BusinessObject = "WechatAccessToken",
                ObjectId = wechatAccessToken.Id.ToString(),
                DetailedInfo = $"创建微信访问令牌，AppId：{createDto.AppId}，类型：{createDto.TokenType}",
                AfterData = new { wechatAccessToken.AppId, wechatAccessToken.TokenType, wechatAccessToken.ExpiresTime },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return wechatAccessToken.Id;
        }

        /// <summary>
        /// 更新访问令牌
        /// </summary>
        /// <param name="updateDto">更新访问令牌DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAccessTokenAsync(WechatAccessTokenUpdateDto updateDto, CurrentUserInfoDto currentUserInfo)
        {
            // 获取原令牌信息
            var wechatAccessToken = await _wechatAccessTokenDAL.GetByIdAsync(updateDto.Id)
                ?? throw new BusinessException("微信访问令牌不存在");

            var beforeData = new { wechatAccessToken.AccessToken, wechatAccessToken.ExpiresIn, wechatAccessToken.RefreshToken };

            // 更新令牌信息
            wechatAccessToken.AccessToken = updateDto.AccessToken ?? string.Empty;
            wechatAccessToken.ExpiresIn = updateDto.ExpiresIn;
            wechatAccessToken.ExpiresTime = updateDto.ExpiresIn.HasValue ? DateTime.Now.AddSeconds((double)updateDto.ExpiresIn.Value) : null;
            wechatAccessToken.RefreshToken = updateDto.RefreshToken;
            wechatAccessToken.Scope = updateDto.Scope;

            var result = await _wechatAccessTokenDAL.UpdateAsync(wechatAccessToken);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信令牌",
                Operation = "更新访问令牌",
                BusinessObject = "WechatAccessToken",
                ObjectId = wechatAccessToken.Id.ToString(),
                DetailedInfo = $"更新微信访问令牌，AppId：{wechatAccessToken.AppId}",
                BeforeData = beforeData,
                AfterData = new { wechatAccessToken.AccessToken, wechatAccessToken.ExpiresIn, wechatAccessToken.RefreshToken },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="tokenType">令牌类型</param>
        /// <param name="newAccessToken">新访问令牌</param>
        /// <param name="expiresIn">过期时间（秒）</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> RefreshAccessTokenAsync(string appId, string tokenType, string newAccessToken, int expiresIn, CurrentUserInfoDto currentUserInfo)
        {
            var result = await _wechatAccessTokenDAL.RefreshAccessTokenAsync(appId, newAccessToken, expiresIn);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信令牌",
                Operation = "刷新访问令牌",
                BusinessObject = "WechatAccessToken",
                ObjectId = $"{appId}_{tokenType}",
                DetailedInfo = $"刷新微信访问令牌，AppId：{appId}，类型：{tokenType}",
                AfterData = new { AppId = appId, TokenType = tokenType, ExpiresIn = expiresIn },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 激活/停用令牌
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <param name="isActive">是否激活</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ToggleTokenStatusAsync(int tokenId, byte isActive, CurrentUserInfoDto currentUserInfo)
        {
            var token = await _wechatAccessTokenDAL.GetByIdAsync(tokenId)
                ?? throw new BusinessException("微信访问令牌不存在");

            var beforeStatus = token.IsActive;
            var result = await _wechatAccessTokenDAL.UpdateTokenStatusAsync(tokenId, isActive);

            // 记录业务日志
            var statusText = isActive == 1 ? "激活" : "停用";
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信令牌",
                Operation = $"{statusText}令牌",
                BusinessObject = "WechatAccessToken",
                ObjectId = tokenId.ToString(),
                DetailedInfo = $"{statusText}微信访问令牌，AppId：{token.AppId}",
                BeforeData = new { IsActive = beforeStatus },
                AfterData = new { IsActive = isActive },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 获取有效的访问令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>访问令牌</returns>
        public async Task<string?> GetValidAccessTokenAsync(string appId, string tokenType)
        {
            var token = await _wechatAccessTokenDAL.GetValidAccessTokenAsync(appId);
            return token?.AccessToken;
        }

        /// <summary>
        /// 检查令牌是否即将过期
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="tokenType">令牌类型</param>
        /// <param name="bufferMinutes">缓冲时间（分钟）</param>
        /// <returns>是否即将过期</returns>
        public async Task<bool> IsTokenExpiringAsync(string appId, string tokenType, int bufferMinutes = 10)
        {
            return await _wechatAccessTokenDAL.IsTokenExpiringAsync(appId, bufferMinutes);
        }

        /// <summary>
        /// 获取访问令牌详情
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>访问令牌响应DTO</returns>
        public async Task<WechatAccessTokenResponseDto?> GetAccessTokenAsync(int tokenId)
        {
            var wechatAccessToken = await _wechatAccessTokenDAL.GetByIdAsync(tokenId);
            if (wechatAccessToken == null) return null;

            return new WechatAccessTokenResponseDto
            {
                Id = wechatAccessToken.Id,
                AppId = wechatAccessToken.AppId,
                TokenType = wechatAccessToken.TokenType,
                AccessToken = wechatAccessToken.AccessToken,
                ExpiresAt = wechatAccessToken.ExpiresAt,
                ExpiresIn = wechatAccessToken.ExpiresIn ?? 0,
                ExpiresTime = wechatAccessToken.ExpiresTime ?? DateTime.MinValue,
                RefreshToken = wechatAccessToken.RefreshToken,
                Scope = wechatAccessToken.Scope,
                IsValid = wechatAccessToken.IsValid,
                IsActive = wechatAccessToken.IsActive == 1,
                ObtainTime = wechatAccessToken.ObtainTime,
                CreateTime = wechatAccessToken.CreateTime
            };
        }

        /// <summary>
        /// 分页查询访问令牌列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<WechatAccessTokenResponseDto>> GetAccessTokenPagedListAsync(WechatAccessTokenQueryDto queryDto)
        {
            var queryable = new WechatAccessTokenDAL.Queryable
            {
                AppId = queryDto.AppId,
                TokenType = queryDto.TokenType,
                IsActive = queryDto.IsActive.HasValue ? (byte?)(queryDto.IsActive.Value ? 1 : 0) : null,
                IsExpired = queryDto.IsExpired.HasValue ? (byte?)(queryDto.IsExpired.Value ? 1 : 0) : null,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var result = await _wechatAccessTokenDAL.GetPagedListAsync(queryable);

            var responseList = (result.Items ?? []).Select(token => new WechatAccessTokenResponseDto
            {
                Id = token.Id,
                AppId = token.AppId,
                TokenType = token.TokenType,
                AccessToken = token.AccessToken,
                ExpiresAt = token.ExpiresAt,
                ExpiresIn = token.ExpiresIn ?? 0,
                ExpiresTime = token.ExpiresTime ?? DateTime.MinValue,
                RefreshToken = token.RefreshToken,
                Scope = token.Scope,
                IsValid = token.IsValid,
                IsActive = token.IsActive == 1,
                ObtainTime = token.ObtainTime,
                CreateTime = token.CreateTime
            }).ToList();

            return new PagedResult<WechatAccessTokenResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 获取应用的所有令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="isActive">是否激活（可选）</param>
        /// <returns>访问令牌列表</returns>
        public async Task<List<WechatAccessTokenResponseDto>> GetAppTokensAsync(string appId, byte? isActive = null)
        {
            // GetByAppIdAsync只接受一个参数，需要客户端过滤
            var token = await _wechatAccessTokenDAL.GetByAppIdAsync(appId);
            var tokens = token != null ? [token] : [];

            if (isActive.HasValue)
            {
                tokens = [.. tokens.Where(t => t.IsActive == isActive.Value)];
            }

            return [.. tokens.Select(token => new WechatAccessTokenResponseDto
            {
                Id = token.Id,
                AppId = token.AppId,
                TokenType = token.TokenType,
                AccessToken = token.AccessToken,
                ExpiresAt = token.ExpiresAt,
                ExpiresIn = token.ExpiresIn ?? 0,
                ExpiresTime = token.ExpiresTime ?? DateTime.MinValue,
                RefreshToken = token.RefreshToken,
                Scope = token.Scope,
                IsValid = token.IsValid,
                IsActive = token.IsActive == 1,
                ObtainTime = token.ObtainTime,
                CreateTime = token.CreateTime
            })];
        }

        /// <summary>
        /// 获取即将过期的令牌列表
        /// </summary>
        /// <param name="bufferMinutes">缓冲时间（分钟）</param>
        /// <returns>即将过期的令牌列表</returns>
        public async Task<List<WechatAccessTokenResponseDto>> GetExpiringTokensAsync(int bufferMinutes = 10)
        {
            var tokens = await _wechatAccessTokenDAL.GetExpiringTokensAsync(bufferMinutes);

            return [.. tokens.Select(token => new WechatAccessTokenResponseDto
            {
                Id = token.Id,
                AppId = token.AppId,
                TokenType = token.TokenType,
                AccessToken = token.AccessToken,
                ExpiresAt = token.ExpiresAt,
                ExpiresIn = token.ExpiresIn ?? 0,
                ExpiresTime = token.ExpiresTime ?? DateTime.MinValue,
                RefreshToken = token.RefreshToken,
                Scope = token.Scope,
                IsValid = token.IsValid,
                IsActive = token.IsActive == 1,
                ObtainTime = token.ObtainTime,
                CreateTime = token.CreateTime
            })];
        }

        /// <summary>
        /// 清理过期令牌
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>清理的记录数</returns>
        public async Task<int> CleanupExpiredTokensAsync(CurrentUserInfoDto currentUserInfo)
        {
            var deletedCount = await _wechatAccessTokenDAL.CleanupExpiredTokensAsync();

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信令牌",
                Operation = "清理过期令牌",
                BusinessObject = "WechatAccessToken",
                ObjectId = "Cleanup",
                DetailedInfo = $"清理过期的微信访问令牌，共清理 {deletedCount} 条记录",
                AfterData = new { DeletedCount = deletedCount },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return deletedCount;
        }

        /// <summary>
        /// 获取令牌统计信息
        /// </summary>
        /// <returns>令牌统计</returns>
        public async Task<WechatTokenStatisticsDto> GetTokenStatisticsAsync()
        {
            var statistics = await _wechatAccessTokenDAL.GetTokenStatisticsAsync();

            return new WechatTokenStatisticsDto
            {
                TotalCount = statistics.TotalCount,
                ActiveCount = statistics.ActiveCount,
                ExpiredCount = statistics.ExpiredCount,
                ExpiringCount = statistics.ExpiringCount,
                AppCount = statistics.AppCount,
                TokenTypeStats = statistics.TokenTypeStats
            };
        }

        /// <summary>
        /// 批量刷新令牌
        /// </summary>
        /// <param name="refreshRequests">刷新请求列表</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>刷新结果</returns>
        public async Task<BatchRefreshResultDto> BatchRefreshTokensAsync(List<TokenRefreshRequestDto> refreshRequests, CurrentUserInfoDto currentUserInfo)
        {
            var successCount = 0;
            var failureCount = 0;
            var results = new List<TokenRefreshResultItemDto>();

            foreach (var request in refreshRequests)
            {
                try
                {
                    var success = await _wechatAccessTokenDAL.RefreshAccessTokenAsync(
                        request.AppId ?? string.Empty,
                        request.NewAccessToken ?? string.Empty,
                        request.ExpiresIn);

                    if (success)
                    {
                        successCount++;
                        results.Add(new TokenRefreshResultItemDto
                        {
                            AppId = request.AppId,
                            TokenType = request.TokenType,
                            Success = true,
                            Message = "刷新成功"
                        });
                    }
                    else
                    {
                        failureCount++;
                        results.Add(new TokenRefreshResultItemDto
                        {
                            AppId = request.AppId,
                            TokenType = request.TokenType,
                            Success = false,
                            Message = "刷新失败"
                        });
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    results.Add(new TokenRefreshResultItemDto
                    {
                        AppId = request.AppId,
                        TokenType = request.TokenType,
                        Success = false,
                        Message = ex.Message
                    });
                }
            }

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信令牌",
                Operation = "批量刷新令牌",
                BusinessObject = "WechatAccessToken",
                ObjectId = "BatchRefresh",
                DetailedInfo = $"批量刷新微信访问令牌，成功：{successCount}，失败：{failureCount}",
                AfterData = new { SuccessCount = successCount, FailureCount = failureCount },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return new BatchRefreshResultDto
            {
                TotalCount = refreshRequests.Count,
                SuccessCount = successCount,
                FailureCount = failureCount,
                Results = results
            };
        }
    }
}
