<template>
  <view class="wx-center-container">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <u-cell-group :border="false">
        <u-cell :border="false" @click="refreshUserInfo">
          <template #icon>
            <view class="avatar-container">
              <u-avatar :src="currentUser.avatar || '/assets/images/avatar-placeholder.png'" size="60"
                shape="square"></u-avatar>
            </view>
          </template>
          <template #title>
            <view class="user-info">
              <view class="user-name">{{ currentUser.realName || currentUser.name || '用户' }}</view>
              <view class="user-meta">
                <text class="user-phone">{{ currentUser.phone || currentUser.username || '' }}</text>
                <u-tag :text="userTypeText" type="primary" size="mini" :plain="true"></u-tag>
              </view>
              <view class="user-extra-info" v-if="currentUser.email || currentUser.department">
                <text class="user-email" v-if="currentUser.email">{{ currentUser.email }}</text>
                <text class="user-department" v-if="currentUser.department">{{ currentUser.department }}</text>
              </view>
            </view>
          </template>
          <template #right-icon>
            <u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
          </template>
        </u-cell>
      </u-cell-group>
    </view>


    <!-- 主要功能菜单 -->
    <view class="menu-section" v-if="visibleMainPages.length > 0">
      <u-cell-group :border="false">
        <u-cell v-for="(item, index) in visibleMainPages" :key="index" :title="item.name" :isLink="true"
          :border="index !== visibleMainPages.length - 1" @click="navigateTo(item.path)">
          <template #icon>
            <u-icon :name="getIconName(item.icon)" size="20" color="#666"></u-icon>
          </template>
        </u-cell>
      </u-cell-group>
    </view>

    <!-- 个人设置菜单 -->
    <view class="menu-section" v-if="visiblePersonalPages.length > 0">
      <u-cell-group :border="false">
        <u-cell v-for="(item, index) in visiblePersonalPages" :key="index" :title="item.name" :isLink="true"
          :border="index !== visiblePersonalPages.length - 1" @click="navigateTo(item.path)">
          <template #icon>
            <u-icon :name="getIconName(item.icon)" size="20" color="#666"></u-icon>
          </template>
        </u-cell>
      </u-cell-group>
    </view>

    <!-- 用户管理菜单 -->
    <view class="menu-section" v-if="visibleUserPages.length > 0">
      <u-cell-group :border="false">
        <u-cell v-for="(item, index) in visibleUserPages" :key="index" :title="item.name" :isLink="true"
          :border="index !== visibleUserPages.length - 1" @click="navigateTo(item.path)">
          <template #icon>
            <u-icon :name="getIconName(item.icon)" size="20" color="#666"></u-icon>
          </template>
        </u-cell>
      </u-cell-group>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <u-cell-group :border="false">
        <u-cell title="退出登录" :isLink="true" :border="false" @click="showLogoutConfirm">
          <template #title>
            <text class="logout-text">退出登录</text>
          </template>
        </u-cell>
      </u-cell-group>
    </view>
  </view>
</template>

<script>
import { getUserDisplayInfo, getCompleteUserDisplayInfo, logoutWithConfirm, requireAnyAuth } from '../../utils/auth-guard.js'
import permissionMixin from '../../mixins/permission-mixin.js'

export default {
  mixins: [permissionMixin],
  data () {
    return {
      currentUser: {},
      loading: false,
      // 主要功能
      mainPages: [
        {
          name: "数据统计",
          path: "/pages/index/index",
          icon: "icon-dashboard",
          requiredPermission: "view_dashboard", // 需要的权限
        }
      ],
      // 个人设置
      personalPages: [
        {
          name: "修改密码",
          path: "/pages/user/change-password",
          icon: "icon-lock",
          requiredPermission: null, // 所有登录用户都能访问
        },
      ],
      // 用户管理页面
      userPages: [
        {
          name: "管理信息",
          path: "/pages/admin/users/manager-list",
          icon: "icon-users",
          requiredRoles: ["admin", "super_admin"], // 需要的角色
        },
        {
          name: "员工信息",
          path: "/pages/admin/users/employee-list",
          icon: "icon-staff",
          requiredRoles: ["manager", "agent", "admin", "super_admin"],
        },
        {
          name: "用户信息",
          path: "/pages/admin/users/user-list",
          icon: "icon-user",
          requiredPermission: "manage_users",
        },
        {
          name: "用户审核",
          path: "/pages/admin/users/audit/user-audit",
          icon: "icon-audit",
          requiredPermission: "user_audit",
        },
      ],
    };
  },
  computed: {
    userTypeText () {
      const typeMap = {
        'employee': '员工',
        'manager': '管理',
        'admin': '超管',
        'super_admin': '超管',
        'agent': '管理'
      };
      return typeMap[this.currentUser.userType] || '用户';
    },

    formatLastLoginTime () {
      if (!this.currentUser.lastLoginTime) return '从未登录';

      try {
        const date = new Date(this.currentUser.lastLoginTime);
        const now = new Date();
        const diff = now - date;

        // 如果是今天
        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
          return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }

        // 如果是昨天
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth()) {
          return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }

        // 其他情况显示完整日期
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (error) {
        return this.currentUser.lastLoginTime;
      }
    },

    // 过滤有权限的主要功能菜单
    visibleMainPages () {
      return this.mainPages.filter(page => this.hasPagePermission(page));
    },

    // 过滤有权限的个人设置菜单
    visiblePersonalPages () {
      return this.personalPages.filter(page => this.hasPagePermission(page));
    },

    // 过滤有权限的用户管理菜单
    visibleUserPages () {
      return this.userPages.filter(page => this.hasPagePermission(page));
    }
  },
  onLoad (options) {
    // 使用认证守卫检查登录状态
    if (!requireAnyAuth(options)) {
      return;
    }

    this.loadUserInfo();
  },
  methods: {
    // 检查页面权限
    hasPagePermission (page) {
      console.log(`检查页面权限: ${page.name}`, {
        requiredPermission: page.requiredPermission,
        requiredRoles: page.requiredRoles,
        currentUserType: this.currentUserType
      });

      // 超管拥有所有权限
      if (this.currentUserType === 'admin' || this.currentUserType === 'super_admin') {
        console.log(`${page.name}: 超管权限，允许访问`);
        return true;
      }

      // 如果没有权限要求，所有登录用户都能访问
      if (!page.requiredPermission && !page.requiredRoles) {
        console.log(`${page.name}: 无权限要求，允许访问`);
        return true;
      }

      // 检查功能权限
      if (page.requiredPermission) {
        const hasFeature = this.canUseFeature(page.requiredPermission);
        console.log(`${page.name}: 功能权限检查 ${page.requiredPermission} = ${hasFeature}`);
        return hasFeature;
      }

      // 检查角色权限
      if (page.requiredRoles) {
        const hasRolePermission = this.hasRole(page.requiredRoles);
        console.log(`${page.name}: 角色权限检查 ${page.requiredRoles} = ${hasRolePermission}`);
        return hasRolePermission;
      }

      console.log(`${page.name}: 默认拒绝访问`);
      return false;
    },

    async loadUserInfo () {
      try {
        // 首先获取缓存的用户信息
        this.currentUser = getUserDisplayInfo();
        console.log('缓存的用户信息:', this.currentUser);

        // 然后异步获取完整的用户信息（包括服务器最新数据）
        const completeUserInfo = await getCompleteUserDisplayInfo(false);
        if (completeUserInfo) {
          this.currentUser = completeUserInfo;
          console.log('完整的用户信息:', this.currentUser);
        }

        // 调试权限信息
        console.log('=== 权限调试信息 ===');
        console.log('当前用户信息:', this.currentUser);
        console.log('当前用户类型:', this.currentUserType);
        console.log('当前用户权限:', this.currentPermissions);
        console.log('是否为超管:', this.isAdmin);
        console.log('是否为管理员:', this.isManager);
        console.log('权限级别:', this.currentPermissionLevel);

        // 测试具体权限
        console.log('测试权限 - view_dashboard:', this.canUseFeature('view_dashboard'));
        console.log('测试权限 - manage_users:', this.canUseFeature('manage_users'));
        console.log('测试权限 - user_audit:', this.canUseFeature('user_audit'));

        console.log('可见主要页面:', this.visibleMainPages);
        console.log('可见个人页面:', this.visiblePersonalPages);
        console.log('可见用户页面:', this.visibleUserPages);
        console.log('===================');
      } catch (error) {
        console.error('加载用户信息失败:', error);
        // 如果获取失败，至少显示缓存的信息
        this.currentUser = getUserDisplayInfo();
      }
    },

    async refreshUserInfo () {
      if (this.loading) return;

      this.loading = true;
      uni.showLoading({
        title: '刷新用户信息...'
      });

      try {
        // 强制从服务器刷新用户信息
        const completeUserInfo = await getCompleteUserDisplayInfo(true);
        if (completeUserInfo) {
          this.currentUser = completeUserInfo;
          uni.showToast({
            title: '用户信息已更新',
            icon: 'success'
          });
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error);
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        uni.hideLoading();
      }
    },

    copyUserId () {
      if (!this.currentUser.userId) {
        uni.showToast({
          title: '用户ID不存在',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: this.currentUser.userId.toString(),
        success: () => {
          uni.showToast({
            title: '用户ID已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    navigateTo (path) {
      // 导航到指定页面
      uni.navigateTo({
        url: path,
        fail: () => {
          // 如果无法使用navigateTo，尝试使用switchTab
          uni.switchTab({
            url: path,
            fail: () => {
              // 如果都失败，则使用reLaunch
              uni.reLaunch({
                url: path,
              });
            },
          });
        },
      });
    },

    showLogoutConfirm () {
      // 使用认证守卫工具的退出确认功能
      logoutWithConfirm();
    },

    // 图标名称映射
    getIconName (iconClass) {
      const iconMap = {
        'icon-dashboard': 'grid',
        'icon-video': 'play-circle',
        'icon-users': 'account',
        'icon-staff': 'account-fill',
        'icon-user': 'account',
        'icon-info': 'info-circle',
        'icon-audit': 'checkmark-circle',
        'icon-lock': 'lock'
      };
      return iconMap[iconClass] || 'more-circle';
    }
  },
};
</script>

<style lang="scss" scoped>
.wx-center-container {
  background-color: #ededed;
}

/* 用户信息头部 */
.user-header {
  overflow: hidden;
  background-color: white;
}

.avatar-container {
  margin-right: 24rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #333;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-phone {
  font-size: 26rpx;
  color: #666;
}

.user-extra-info {
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-email {
  font-size: 24rpx;
  color: #999;
}

.user-department {
  font-size: 24rpx;
  color: #999;
}

/* 用户详细信息区域 */
.user-details-section {
  margin: 20rpx 0rpx;
  overflow: hidden;
  background-color: white;
}

/* 菜单区域 */
.menu-section {
  margin: 20rpx 0rpx;
  overflow: hidden;
  background-color: white;
}

/* 退出登录区域 */
.logout-section {
  overflow: hidden;
  background-color: white;
}

.logout-text {
  color: #f56c6c;
  font-weight: 500;
}
</style>